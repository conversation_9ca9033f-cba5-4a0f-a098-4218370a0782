# LUMIÈRE Boulevard Landing Page

Đây là bản clone hoàn chỉnh của trang web https://masterisehomes.com/lumiere-boulevard/vn được tạo thành landing page có thể tùy chỉnh.

## Tính năng

### ✨ Tính năng chính
- **Hero Slider**: Slideshow tự động với 3 slide, có thể điều khiển bằng nút bấm, indicator, hoặc vuốt trên mobile
- **Navigation Menu**: Menu điều hướng responsive với dropdown và language selector
- **Responsive Design**: Tương thích hoàn hảo trên desktop, tablet và mobile
- **Smooth Scrolling**: Cuộn mượt mà giữa các section
- **Contact Form**: Form liên hệ với validation đầy đủ
- **Image Gallery**: Thư viện ảnh với hiệu ứng hover và lazy loading
- **Parallax Effect**: Hiệu ứng parallax cho hero section

### 🎨 Thiết kế
- **Modern UI/UX**: Thi<PERSON><PERSON> kế hiện đại, chuyên nghiệp
- **Animation**: <PERSON><PERSON><PERSON> hiệu ứng chuyển động mượt mà
- **Color Scheme**: Bảng màu vàng gold (#d4af37) làm màu chủ đạo
- **Typography**: Font Inter cho độ rõ nét và dễ đọc
- **Icons**: Font Awesome icons

### 📱 Responsive
- **Mobile First**: Thiết kế ưu tiên mobile
- **Breakpoints**: Tối ưu cho các kích thước màn hình khác nhau
- **Touch Support**: Hỗ trợ cử chỉ vuốt trên mobile

## Cấu trúc file

```
├── index.html          # File HTML chính
├── styles.css          # File CSS styling
├── script.js           # File JavaScript functionality
└── README.md           # File hướng dẫn này
```

## Cách sử dụng

### 1. Mở trang web
Mở file `index.html` trong trình duyệt web để xem trang.

### 2. Tùy chỉnh nội dung
- **Thay đổi text**: Chỉnh sửa nội dung trong file `index.html`
- **Thay đổi hình ảnh**: Thay thế URL hình ảnh trong file HTML
- **Thay đổi màu sắc**: Chỉnh sửa biến màu trong file `styles.css`
- **Thay đổi font**: Cập nhật Google Fonts import trong HTML

### 3. Tùy chỉnh styling
Các class CSS chính:
- `.hero`: Hero section với slider
- `.architecture`: Section kiến trúc
- `.resort-living`: Section tiện ích
- `.lifestyle-gallery`: Thư viện ảnh lifestyle
- `.location`: Section vị trí
- `.contact-form`: Form liên hệ
- `.footer`: Footer

### 4. Tùy chỉnh JavaScript
Các class JavaScript chính:
- `HeroSlider`: Điều khiển slideshow
- `HeaderController`: Hiệu ứng header khi scroll
- `ContactFormController`: Xử lý form liên hệ
- `MobileMenuController`: Menu mobile
- `ParallaxController`: Hiệu ứng parallax

## Tùy chỉnh cho dự án của bạn

### Thay đổi thông tin công ty
1. **Logo**: Thay thế URL logo trong header và footer
2. **Tên công ty**: Tìm và thay thế "LUMIÈRE Boulevard" và "Masterise Homes"
3. **Thông tin liên hệ**: Cập nhật địa chỉ, số điện thoại, email trong footer
4. **Social media**: Cập nhật link social media

### Thay đổi nội dung
1. **Hero titles**: Chỉnh sửa các tiêu đề trong hero slider
2. **Section content**: Cập nhật nội dung các section
3. **Gallery images**: Thay thế hình ảnh trong gallery
4. **Partners**: Thay đổi logo đối tác

### Thay đổi màu sắc chủ đạo
Tìm và thay thế màu `#d4af37` trong file CSS bằng màu mong muốn:
```css
/* Ví dụ thay đổi thành màu xanh */
color: #2196F3;
background: #2196F3;
border-color: #2196F3;
```

### Thêm section mới
1. Thêm HTML structure trong `index.html`
2. Thêm CSS styling trong `styles.css`
3. Thêm JavaScript functionality nếu cần trong `script.js`

## Browser Support
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## Performance
- **Lazy Loading**: Hình ảnh được load khi cần thiết
- **Optimized CSS**: CSS được tối ưu cho performance
- **Debounced Events**: Các event scroll được debounce
- **Efficient Animations**: Sử dụng CSS transforms cho animation mượt

## SEO Ready
- Semantic HTML structure
- Meta tags optimization
- Alt text cho images
- Proper heading hierarchy

## Accessibility
- Keyboard navigation support
- Screen reader friendly
- High contrast colors
- Focus indicators

## License
Đây là bản clone cho mục đích học tập và phát triển. Vui lòng tuân thủ bản quyền của trang web gốc khi sử dụng thương mại.

## Hỗ trợ
Nếu bạn cần hỗ trợ tùy chỉnh thêm, vui lòng liên hệ để được hướng dẫn chi tiết.
