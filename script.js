/* ===== LUMIÈRE BOULEVARD JAVASCRIPT ===== */

$(document).ready(function() {

    // Header scroll effect
    $(window).scroll(function() {
        if ($(this).scrollTop() > 100) {
            $('.header-main').addClass('scrolled');
        } else {
            $('.header-main').removeClass('scrolled');
        }
    });

    // Smooth scrolling for navigation links
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();

        var target = this.hash;
        var $target = $(target);

        if ($target.length) {
            $('html, body').animate({
                scrollTop: $target.offset().top - 80
            }, 800, 'swing');
        }
    });

    // Hero carousel custom controls
    $('#heroCarousel').on('slide.bs.carousel', function(e) {
        // Add custom slide transition effects here if needed
        console.log('Sliding to slide:', e.to);
    });

    // Pause carousel on hover
    $('#heroCarousel').hover(
        function() {
            $(this).carousel('pause');
        },
        function() {
            $(this).carousel('cycle');
        }
    );

    // Gallery item hover effects
    $('.gallery-item').hover(
        function() {
            $(this).find('.gallery-overlay').fadeIn(300);
        },
        function() {
            $(this).find('.gallery-overlay').fadeOut(300);
        }
    );

    // Map tab functionality
    $('.map-tab').click(function() {
        $('.map-tab').removeClass('active');
        $(this).addClass('active');

        var tabType = $(this).data('tab');
        console.log('Switched to map tab:', tabType);
        // Add map switching logic here
    });

    // Contact form handling
    $('#contactForm').submit(function(e) {
        e.preventDefault();

        var formData = {
            fullname: $('#fullname').val(),
            phone: $('#phone').val(),
            email: $('#email').val()
        };

        // Basic validation
        if (!formData.fullname || !formData.phone || !formData.email) {
            alert('Vui lòng điền đầy đủ thông tin!');
            return;
        }

        // Email validation
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email)) {
            alert('Vui lòng nhập email hợp lệ!');
            return;
        }

        // Phone validation (Vietnamese phone number)
        var phoneRegex = /^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/;
        if (!phoneRegex.test(formData.phone.replace(/\s/g, ''))) {
            alert('Vui lòng nhập số điện thoại hợp lệ!');
            return;
        }

        // Show loading
        var $submitBtn = $(this).find('button[type="submit"]');
        $submitBtn.prop('disabled', true).text('Đang gửi...');

        // Simulate form submission
        setTimeout(function() {
            $submitBtn.prop('disabled', false).text('Submit');
            alert('Cảm ơn bạn đã đăng ký! Chúng tôi sẽ liên hệ với bạn sớm nhất có thể.');
            $('#contactForm')[0].reset();
        }, 2000);
    });

    // Animate elements on scroll
    function animateOnScroll() {
        $('.section-header, .architecture-content, .resort-content, .service-content, .location-content').each(function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();

            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                $(this).addClass('animate-in');
            }
        });
    }

    // Run animation check on scroll
    $(window).scroll(animateOnScroll);

    // Run animation check on page load
    animateOnScroll();

    // Parallax effect for hero background
    $(window).scroll(function() {
        var scrolled = $(this).scrollTop();
        var rate = scrolled * -0.5;
        $('.hero-bg').css('transform', 'translateY(' + rate + 'px)');
    });

    // Loading animation
    $('body').css('opacity', '0').animate({opacity: 1}, 500);

    // Mobile menu enhancements
    $('.navbar-toggler').click(function() {
        $(this).toggleClass('active');
    });

    // Close mobile menu when clicking on a link
    $('.navbar-nav .nav-link').click(function() {
        if ($('.navbar-collapse').hasClass('show')) {
            $('.navbar-toggler').click();
        }
    });

    // Add touch/swipe support for carousel on mobile
    var startX, startY, endX, endY;

    $('#heroCarousel').on('touchstart', function(e) {
        startX = e.originalEvent.touches[0].clientX;
        startY = e.originalEvent.touches[0].clientY;
    });

    $('#heroCarousel').on('touchend', function(e) {
        endX = e.originalEvent.changedTouches[0].clientX;
        endY = e.originalEvent.changedTouches[0].clientY;

        var diffX = startX - endX;
        var diffY = startY - endY;

        // Only trigger if horizontal swipe is greater than vertical
        if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
            if (diffX > 0) {
                // Swipe left - next slide
                $(this).carousel('next');
            } else {
                // Swipe right - previous slide
                $(this).carousel('prev');
            }
        }
    });

    // ===== HIỆU ỨNG THEO TÀI LIỆU THIẾT KẾ =====

    // Button hover effects - scale + đổi màu nền sang vàng kim
    $('.btn-primary, .btn-secondary, .cta-button').hover(
        function() {
            $(this).css({
                'transform': 'scale(1.05)',
                'background-color': 'var(--secondary-gold)',
                'border-color': 'var(--secondary-gold)',
                'transition': 'all 0.3s ease'
            });
        },
        function() {
            $(this).css({
                'transform': 'scale(1)',
                'background-color': 'var(--primary-green)',
                'border-color': 'var(--primary-green)'
            });
        }
    );

    // Gallery hover effects - phóng to + overlay mờ
    $('.gallery-item').hover(
        function() {
            $(this).find('img').css({
                'transform': 'scale(1.1)',
                'transition': 'transform 0.3s ease'
            });
            $(this).find('.gallery-overlay').css('opacity', '1');
        },
        function() {
            $(this).find('img').css('transform', 'scale(1)');
            $(this).find('.gallery-overlay').css('opacity', '0.8');
        }
    );

    // Bootstrap tab fade effect
    $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
        $(e.target.getAttribute('href')).addClass('fade show');
    });

    // Masterplan tooltip + hover vùng click
    $('.map-area, [data-bs-toggle="tooltip"]').hover(
        function() {
            $(this).css({
                'opacity': '0.8',
                'transition': 'opacity 0.3s ease'
            });
        },
        function() {
            $(this).css('opacity', '1');
        }
    );

    // Hero section text fade-in animation
    $('.hero-content').addClass('animate-on-scroll');

    console.log('LUMIÈRE Boulevard website loaded successfully!');
});