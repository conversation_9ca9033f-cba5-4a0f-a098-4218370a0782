// Hero Slider Functionality
class HeroSlider {
    constructor() {
        this.slides = document.querySelectorAll('.hero-slide');
        this.indicators = document.querySelectorAll('.indicator');
        this.prevBtn = document.querySelector('.hero-prev');
        this.nextBtn = document.querySelector('.hero-next');
        this.currentSlide = 0;
        this.slideInterval = null;
        
        this.init();
    }
    
    init() {
        // Add event listeners
        this.prevBtn.addEventListener('click', () => this.prevSlide());
        this.nextBtn.addEventListener('click', () => this.nextSlide());
        
        // Add indicator click events
        this.indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => this.goToSlide(index));
        });
        
        // Start auto-slide
        this.startAutoSlide();
        
        // Pause on hover
        const heroSection = document.querySelector('.hero');
        heroSection.addEventListener('mouseenter', () => this.stopAutoSlide());
        heroSection.addEventListener('mouseleave', () => this.startAutoSlide());
    }
    
    goToSlide(slideIndex) {
        // Remove active class from current slide and indicator
        this.slides[this.currentSlide].classList.remove('active');
        this.indicators[this.currentSlide].classList.remove('active');
        
        // Update current slide
        this.currentSlide = slideIndex;
        
        // Add active class to new slide and indicator
        this.slides[this.currentSlide].classList.add('active');
        this.indicators[this.currentSlide].classList.add('active');
    }
    
    nextSlide() {
        const nextIndex = (this.currentSlide + 1) % this.slides.length;
        this.goToSlide(nextIndex);
    }
    
    prevSlide() {
        const prevIndex = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
        this.goToSlide(prevIndex);
    }
    
    startAutoSlide() {
        this.slideInterval = setInterval(() => {
            this.nextSlide();
        }, 5000); // Change slide every 5 seconds
    }
    
    stopAutoSlide() {
        if (this.slideInterval) {
            clearInterval(this.slideInterval);
            this.slideInterval = null;
        }
    }
}

// Header Scroll Effect
class HeaderController {
    constructor() {
        this.header = document.querySelector('.header');
        this.init();
    }
    
    init() {
        window.addEventListener('scroll', () => this.handleScroll());
    }
    
    handleScroll() {
        if (window.scrollY > 100) {
            this.header.classList.add('scrolled');
        } else {
            this.header.classList.remove('scrolled');
        }
    }
}

// Mobile Menu Controller
class MobileMenuController {
    constructor() {
        this.toggle = document.querySelector('.mobile-menu-toggle');
        this.navMenu = document.querySelector('.nav-menu');
        this.isOpen = false;
        
        this.init();
    }
    
    init() {
        if (this.toggle) {
            this.toggle.addEventListener('click', () => this.toggleMenu());
        }
        
        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (this.isOpen && !this.toggle.contains(e.target) && !this.navMenu.contains(e.target)) {
                this.closeMenu();
            }
        });
        
        // Close menu on window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768 && this.isOpen) {
                this.closeMenu();
            }
        });
    }
    
    toggleMenu() {
        if (this.isOpen) {
            this.closeMenu();
        } else {
            this.openMenu();
        }
    }
    
    openMenu() {
        this.navMenu.style.display = 'flex';
        this.navMenu.style.flexDirection = 'column';
        this.navMenu.style.position = 'absolute';
        this.navMenu.style.top = '100%';
        this.navMenu.style.left = '0';
        this.navMenu.style.right = '0';
        this.navMenu.style.background = 'white';
        this.navMenu.style.boxShadow = '0 5px 20px rgba(0,0,0,0.1)';
        this.navMenu.style.padding = '20px';
        this.navMenu.style.zIndex = '1000';
        
        this.toggle.classList.add('active');
        this.isOpen = true;
    }
    
    closeMenu() {
        this.navMenu.style.display = '';
        this.navMenu.style.flexDirection = '';
        this.navMenu.style.position = '';
        this.navMenu.style.top = '';
        this.navMenu.style.left = '';
        this.navMenu.style.right = '';
        this.navMenu.style.background = '';
        this.navMenu.style.boxShadow = '';
        this.navMenu.style.padding = '';
        this.navMenu.style.zIndex = '';
        
        this.toggle.classList.remove('active');
        this.isOpen = false;
    }
}

// Smooth Scrolling for Navigation Links
class SmoothScroll {
    constructor() {
        this.init();
    }
    
    init() {
        // Add smooth scrolling to all navigation links
        const navLinks = document.querySelectorAll('a[href^="#"]');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => this.handleClick(e));
        });
    }
    
    handleClick(e) {
        e.preventDefault();
        const targetId = e.target.getAttribute('href');
        const targetElement = document.querySelector(targetId);
        
        if (targetElement) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const targetPosition = targetElement.offsetTop - headerHeight;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    }
}

// Gallery Image Lazy Loading
class LazyImageLoader {
    constructor() {
        this.images = document.querySelectorAll('.gallery-item img');
        this.init();
    }
    
    init() {
        // Create intersection observer for lazy loading
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.style.opacity = '0';
                    img.style.transition = 'opacity 0.5s ease';
                    
                    img.onload = () => {
                        img.style.opacity = '1';
                    };
                    
                    observer.unobserve(img);
                }
            });
        });
        
        this.images.forEach(img => {
            imageObserver.observe(img);
        });
    }
}

// Scroll Animation Controller
class ScrollAnimationController {
    constructor() {
        this.animatedElements = document.querySelectorAll('.section-header, .architecture-content, .resort-content, .service-content, .gallery-item');
        this.init();
    }
    
    init() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });
        
        this.animatedElements.forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';
            element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(element);
        });
    }
}

// Initialize all controllers when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new HeroSlider();
    new HeaderController();
    new MobileMenuController();
    new SmoothScroll();
    new LazyImageLoader();
    new ScrollAnimationController();
    
    // Add loading animation
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    window.addEventListener('load', () => {
        document.body.style.opacity = '1';
    });
});

// Performance optimization: Debounce scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add keyboard navigation for accessibility
document.addEventListener('keydown', (e) => {
    const heroSlider = document.querySelector('.hero');
    if (document.activeElement === heroSlider || heroSlider.contains(document.activeElement)) {
        if (e.key === 'ArrowLeft') {
            document.querySelector('.hero-prev').click();
        } else if (e.key === 'ArrowRight') {
            document.querySelector('.hero-next').click();
        }
    }
});

// Add touch/swipe support for mobile
let touchStartX = 0;
let touchEndX = 0;

document.querySelector('.hero').addEventListener('touchstart', (e) => {
    touchStartX = e.changedTouches[0].screenX;
});

document.querySelector('.hero').addEventListener('touchend', (e) => {
    touchEndX = e.changedTouches[0].screenX;
    handleSwipe();
});

function handleSwipe() {
    const swipeThreshold = 50;
    const diff = touchStartX - touchEndX;

    if (Math.abs(diff) > swipeThreshold) {
        if (diff > 0) {
            // Swipe left - next slide
            document.querySelector('.hero-next').click();
        } else {
            // Swipe right - previous slide
            document.querySelector('.hero-prev').click();
        }
    }
}

// Map Tab Controller
class MapTabController {
    constructor() {
        this.tabs = document.querySelectorAll('.map-tab');
        this.init();
    }

    init() {
        this.tabs.forEach(tab => {
            tab.addEventListener('click', (e) => this.handleTabClick(e));
        });
    }

    handleTabClick(e) {
        // Remove active class from all tabs
        this.tabs.forEach(tab => tab.classList.remove('active'));

        // Add active class to clicked tab
        e.target.classList.add('active');

        // Here you could add logic to switch map content
        const tabType = e.target.getAttribute('data-tab');
        console.log('Switched to map tab:', tabType);
    }
}

// Contact Form Controller
class ContactFormController {
    constructor() {
        this.form = document.querySelector('.contact-form-container');
        this.init();
    }

    init() {
        if (this.form) {
            this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        }
    }

    handleSubmit(e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData(this.form);
        const data = {
            fullname: formData.get('fullname'),
            phone: formData.get('phone'),
            email: formData.get('email')
        };

        // Basic validation
        if (!data.fullname || !data.phone || !data.email) {
            alert('Vui lòng điền đầy đủ thông tin!');
            return;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
            alert('Vui lòng nhập email hợp lệ!');
            return;
        }

        // Phone validation (Vietnamese phone number)
        const phoneRegex = /^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/;
        if (!phoneRegex.test(data.phone.replace(/\s/g, ''))) {
            alert('Vui lòng nhập số điện thoại hợp lệ!');
            return;
        }

        // Simulate form submission
        this.showLoading();

        setTimeout(() => {
            this.hideLoading();
            alert('Cảm ơn bạn đã đăng ký! Chúng tôi sẽ liên hệ với bạn sớm nhất có thể.');
            this.form.reset();
        }, 2000);
    }

    showLoading() {
        const submitBtn = this.form.querySelector('.submit-btn');
        submitBtn.disabled = true;
        submitBtn.textContent = 'Đang gửi...';
    }

    hideLoading() {
        const submitBtn = this.form.querySelector('.submit-btn');
        submitBtn.disabled = false;
        submitBtn.textContent = 'Submit';
    }
}

// Parallax Effect Controller
class ParallaxController {
    constructor() {
        this.parallaxElements = document.querySelectorAll('.hero-bg');
        this.init();
    }

    init() {
        window.addEventListener('scroll', debounce(() => this.handleScroll(), 10));
    }

    handleScroll() {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;

        this.parallaxElements.forEach(element => {
            element.style.transform = `translateY(${rate}px)`;
        });
    }
}

// Initialize new controllers
document.addEventListener('DOMContentLoaded', () => {
    new HeroSlider();
    new HeaderController();
    new MobileMenuController();
    new SmoothScroll();
    new LazyImageLoader();
    new ScrollAnimationController();
    new MapTabController();
    new ContactFormController();
    new ParallaxController();

    // Add loading animation
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';

    window.addEventListener('load', () => {
        document.body.style.opacity = '1';
    });
});
