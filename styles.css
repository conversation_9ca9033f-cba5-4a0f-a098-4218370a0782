/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.header.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar {
  padding: 15px 0;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo {
  height: 40px;
}

.logo img {
  height: 100%;
  transition: opacity 0.3s ease;
}

.logo-white {
  display: none;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 30px;
  align-items: center;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav-menu a:hover {
  color: #d4af37;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 10px 0;
  min-width: 150px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu li {
  padding: 8px 20px;
}

.language-selector {
  position: relative;
  cursor: pointer;
}

.current-lang {
  font-weight: 600;
  color: #d4af37;
}

.lang-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 10px 0;
  min-width: 80px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.language-selector:hover .lang-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.lang-dropdown a {
  display: block;
  padding: 8px 15px;
  text-decoration: none;
  color: #333;
  transition: background 0.3s ease;
}

.lang-dropdown a:hover {
  background: #f5f5f5;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: #333;
  transition: all 0.3s ease;
}

/* Hero Section */
.hero {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.hero-slider {
  height: 100%;
  position: relative;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-slide.active {
  opacity: 1;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 20px;
}

.hero-content h1 {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  animation: fadeInUp 1s ease-out;
}

.hero-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 3;
}

.hero-prev {
  left: 30px;
}

.hero-next {
  right: 30px;
}

.hero-prev,
.hero-next {
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.hero-prev:hover,
.hero-next:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.hero-indicators {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 3;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: white;
  transform: scale(1.2);
}

/* Section Styles */
section {
  padding: 80px 0;
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-header h2 {
  font-size: 1.2rem;
  color: #d4af37;
  font-weight: 600;
  margin-bottom: 10px;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.section-header h3 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  line-height: 1.3;
}

/* Architecture Section */
.architecture {
  background: #f8f9fa;
}

.architecture-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 20px;
  text-align: justify;
}

/* Resort Living Section */
.resort-living {
  background: white;
}

.resort-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 20px;
  text-align: justify;
}

/* Lifestyle Gallery */
.lifestyle-gallery {
  background: #f8f9fa;
  padding: 80px 0;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.gallery-item {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-10px);
}

.gallery-item img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 30px 20px 20px;
}

.gallery-caption h4 {
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.4;
}

/* Quality Service Section */
.quality-service {
  background: white;
}

.service-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 20px;
  text-align: justify;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Location Section */
.location {
  background: white;
}

.location-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 20px;
  text-align: justify;
}

.location-highlights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.highlight-item {
  text-align: center;
  padding: 30px 20px;
  background: #f8f9fa;
  border-radius: 15px;
  transition: transform 0.3s ease;
}

.highlight-item:hover {
  transform: translateY(-5px);
}

.highlight-item i {
  font-size: 3rem;
  color: #d4af37;
  margin-bottom: 20px;
}

.highlight-item h4 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.highlight-item p {
  font-size: 1rem;
  line-height: 1.6;
  color: #666;
}

.map-section {
  margin-top: 60px;
}

.map-tabs {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.map-tab {
  padding: 12px 30px;
  background: transparent;
  border: 2px solid #d4af37;
  color: #d4af37;
  font-weight: 600;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.map-tab.active,
.map-tab:hover {
  background: #d4af37;
  color: white;
}

.map-container {
  height: 400px;
  background: #f8f9fa;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ddd;
}

.map-placeholder {
  text-align: center;
  color: #999;
}

.map-placeholder i {
  font-size: 4rem;
  margin-bottom: 15px;
}

.map-placeholder p {
  font-size: 1.2rem;
  font-weight: 500;
}

/* Developer Section */
.developer {
  background: #f8f9fa;
}

.developer-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 20px;
  text-align: justify;
}

.partners-section {
  margin-top: 50px;
  text-align: center;
}

.partners-section h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 40px;
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 30px;
  align-items: center;
}

.partner-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.partner-item:hover {
  transform: translateY(-5px);
}

.partner-item img {
  max-width: 100%;
  max-height: 60px;
  object-fit: contain;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.partner-item:hover img {
  filter: grayscale(0%);
}

/* Contact Form Section */
.contact-form {
  background: white;
  padding: 80px 0;
}

.contact-form .section-header p {
  font-size: 1.1rem;
  color: #666;
  margin-top: 15px;
}

.contact-form-container {
  max-width: 500px;
  margin: 0 auto;
  margin-top: 40px;
}

.form-group {
  margin-bottom: 25px;
}

.form-group input {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background: #f8f9fa;
}

.form-group input:focus {
  outline: none;
  border-color: #d4af37;
  background: white;
}

.recaptcha-placeholder {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  background: #f8f9fa;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  color: #666;
}

.recaptcha-placeholder i {
  color: #d4af37;
}

.submit-btn {
  width: 100%;
  padding: 15px 30px;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.submit-btn:hover {
  background: #b8941f;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .section-header h3 {
    font-size: 2rem;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .hero-prev,
  .hero-next {
    width: 40px;
    height: 40px;
  }

  .hero-prev {
    left: 15px;
  }

  .hero-next {
    right: 15px;
  }

  .location-highlights {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .partners-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .map-tabs {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .map-tab {
    width: 200px;
  }
}

/* Footer */
.footer {
  background: #1a1a1a;
  color: white;
  padding: 60px 0 30px;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 50px;
  margin-bottom: 40px;
}

.footer-logo img {
  height: 50px;
  margin-bottom: 20px;
}

.footer-logo p {
  font-size: 1rem;
  line-height: 1.6;
  color: #ccc;
}

.footer-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
}

.info-item h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #d4af37;
}

.info-item p,
.info-item a {
  color: #ccc;
  text-decoration: none;
  line-height: 1.6;
  transition: color 0.3s ease;
}

.info-item a:hover {
  color: #d4af37;
}

.social-links {
  display: flex;
  gap: 15px;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #333;
  border-radius: 50%;
  color: white;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: #d4af37;
  transform: translateY(-2px);
}

.footer-bottom {
  border-top: 1px solid #333;
  padding-top: 30px;
  text-align: center;
}

.footer-bottom p {
  font-size: 0.9rem;
  line-height: 1.6;
  color: #999;
  margin-bottom: 15px;
}

.footer-bottom p:last-child {
  margin-bottom: 0;
  font-weight: 600;
  color: #d4af37;
}

/* Additional responsive styles for footer */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer-info {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .social-links {
    justify-content: center;
  }
}
