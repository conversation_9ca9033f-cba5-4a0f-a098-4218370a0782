/* ===== LUMIÈRE BOULEVARD STYLES ===== */

/* Custom CSS Variables */
:root {
  --primary-gold: #d4af37;
  --primary-dark: #1a1a1a;
  --text-dark: #333333;
  --text-light: #666666;
  --bg-light: #f8f9fa;
  --white: #ffffff;
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Base Styles */
body {
  font-family: "Roboto", sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  overflow-x: hidden;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Playfair Display", serif;
  font-weight: 600;
}

.text-gold {
  color: var(--primary-gold) !important;
}

.text-gradient {
  background: linear-gradient(45deg, var(--primary-gold), #f4d03f);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Skip Link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-gold);
  color: white;
  padding: 8px;
  text-decoration: none;
  z-index: 9999;
  border-radius: 4px;
}

.skip-link:focus {
  top: 6px;
}

/* Header Styles */
.header-main {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: var(--transition);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand img {
  height: 45px;
  transition: var(--transition);
}

.navbar-nav .nav-link {
  color: var(--text-dark) !important;
  font-weight: 500;
  padding: 0.75rem 1rem !important;
  transition: var(--transition);
  position: relative;
}

.navbar-nav .nav-link:hover {
  color: var(--primary-gold) !important;
}

.navbar-nav .nav-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--primary-gold);
  transition: var(--transition);
  transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
  width: 80%;
}

.dropdown-menu {
  border: none;
  box-shadow: var(--shadow);
  border-radius: 10px;
  padding: 10px 0;
}

.dropdown-item {
  padding: 10px 20px;
  transition: var(--transition);
}

.dropdown-item:hover {
  background: var(--bg-light);
  color: var(--primary-gold);
}

.language-selector .btn {
  color: var(--primary-gold) !important;
  font-weight: 600;
  text-decoration: none;
}

/* Hero Section */
.hero-section {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.carousel-item {
  height: 100vh;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
}

.carousel-caption {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}

.hero-content h1 {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  animation: fadeInUp 1s ease-out;
}

.carousel-control-prev,
.carousel-control-next {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  backdrop-filter: blur(10px);
  transition: var(--transition);
}

.carousel-control-prev {
  left: 30px;
}

.carousel-control-next {
  right: 30px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
  background: rgba(255, 255, 255, 0.3);
}

.carousel-indicators {
  bottom: 30px;
}

.carousel-indicators [data-bs-target] {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
  transition: var(--transition);
}

.carousel-indicators .active {
  background: var(--white);
  transform: scale(1.2);
}

/* Section Styles */
.section-header {
  margin-bottom: 4rem;
}

.section-subtitle {
  font-size: 1rem;
  letter-spacing: 3px;
  margin-bottom: 1rem;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
}

/* Architecture Section */
.architecture-section {
  background: var(--white);
}

.architecture-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  text-align: justify;
}

/* Resort Living Section */
.resort-living-section {
  background: var(--bg-light);
}

.resort-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  text-align: justify;
}

/* Lifestyle Gallery */
.lifestyle-gallery-section {
  background: var(--white);
}

.gallery-item {
  transition: var(--transition);
  cursor: pointer;
}

.gallery-item:hover {
  transform: translateY(-10px);
}

.gallery-img {
  height: 300px;
  object-fit: cover;
  transition: var(--transition);
}

.gallery-item:hover .gallery-img {
  transform: scale(1.05);
}

.gallery-overlay {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  opacity: 0;
  transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

/* Quality Service Section */
.quality-service-section {
  background: var(--bg-light);
}

.service-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  text-align: justify;
}

/* Location Section */
.location-section {
  background: var(--white);
}

.location-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  text-align: justify;
}

.highlight-item {
  text-align: center;
  padding: 2rem;
  background: var(--bg-light);
  border-radius: 15px;
  transition: var(--transition);
  height: 100%;
}

.highlight-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow);
}

.highlight-item i {
  font-size: 3rem;
  color: var(--primary-gold);
  margin-bottom: 1.5rem;
}

.highlight-item h4 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--text-dark);
}

.highlight-item p {
  color: var(--text-light);
  line-height: 1.6;
}

/* Map Section */
.map-section {
  margin-top: 4rem;
}

.map-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.map-tab {
  padding: 12px 30px;
  background: transparent;
  border: 2px solid var(--primary-gold);
  color: var(--primary-gold);
  font-weight: 600;
  border-radius: 25px;
  transition: var(--transition);
}

.map-tab.active,
.map-tab:hover {
  background: var(--primary-gold);
  color: var(--white);
}

.map-container {
  height: 400px;
  background: var(--bg-light);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ddd;
}

.map-placeholder {
  text-align: center;
  color: var(--text-light);
}

.map-placeholder i {
  font-size: 4rem;
  margin-bottom: 1rem;
}

/* Header Scroll Effect */
.header-main.scrolled {
  background: rgba(255, 255, 255, 0.98) !important;
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.15) !important;
}

.header-main.scrolled .logo-gradient {
  display: none;
}

.header-main.scrolled .logo-white {
  display: block !important;
}

/* Scroll Animations */
.animate-in {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* Contact Form */
.contact-section {
  background: var(--bg-light);
  padding: 5rem 0;
}

.contact-form {
  background: var(--white);
  padding: 3rem;
  border-radius: 15px;
  box-shadow: var(--shadow);
}

.form-control {
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 12px 20px;
  font-size: 1rem;
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
}

.btn-primary {
  background: var(--primary-gold);
  border: none;
  padding: 12px 40px;
  font-weight: 600;
  border-radius: 25px;
  transition: var(--transition);
}

.btn-primary:hover {
  background: #b8941f;
  transform: translateY(-2px);
}

/* Footer */
.footer {
  background: var(--primary-dark);
  color: var(--white);
  padding: 3rem 0 1rem;
}

.footer h5 {
  color: var(--primary-gold);
  margin-bottom: 1.5rem;
}

.footer a {
  color: #ccc;
  text-decoration: none;
  transition: var(--transition);
}

.footer a:hover {
  color: var(--primary-gold);
}

.footer-bottom {
  border-top: 1px solid #444;
  margin-top: 2rem;
  padding-top: 2rem;
  text-align: center;
  color: #999;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .carousel-control-prev,
  .carousel-control-next {
    width: 40px;
    height: 40px;
  }

  .carousel-control-prev {
    left: 15px;
  }

  .carousel-control-next {
    right: 15px;
  }

  .gallery-img {
    height: 250px;
  }

  .contact-form {
    padding: 2rem;
  }

  .navbar-nav {
    text-align: center;
  }

  .language-selector {
    margin-top: 1rem;
  }
}
