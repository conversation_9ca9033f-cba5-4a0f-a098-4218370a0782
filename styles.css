/* ===== LUMIÈRE BOULEVARD - EXACT CLONE STYLES ===== */
@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap");

/* CSS Variables */
:root {
  --primary-green: #144d43;
  --secondary-gold: #bfa16c;
  --bg-white: #ffffff;
  --accent-light: #e9f5f3;
  --text-primary: #222222;
  --text-muted: #666666;
}

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Montserrat", sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: #f5f2ed;
  margin: 0;
  padding-top: 80px;
}

/* Skip Link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--secondary-gold);
  color: white;
  padding: 8px;
  text-decoration: none;
  z-index: 9999;
}

.skip-link:focus {
  top: 6px;
}

/* Typography */
.text-gradient {
  background: linear-gradient(135deg, var(--secondary-gold), #d4c08a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  color: var(--secondary-gold);
  font-size: 0.9rem;
  letter-spacing: 2px;
}

.section-title {
  color: var(--primary-green);
  font-size: 2.5rem;
  margin-bottom: 2rem;
}

/* Header */
.header-main {
  background: var(--primary-green);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-main.scrolled {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.navbar {
  padding: 15px 0;
}

.navbar-brand img {
  height: 40px;
}

.navbar-nav .nav-link {
  color: white !important;
  font-weight: 500;
  padding: 8px 15px !important;
  text-decoration: none;
  font-size: 14px;
  text-transform: uppercase;
}

.navbar-nav .nav-link:hover {
  color: var(--secondary-gold) !important;
}

.dropdown-menu {
  border: none;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  border-radius: 0;
  background: var(--primary-green);
}

.dropdown-item {
  color: white;
  font-size: 14px;
}

.dropdown-item:hover {
  background: var(--secondary-gold);
  color: white;
}

.language-selector .btn {
  color: var(--secondary-gold) !important;
  font-weight: 600;
  text-decoration: none;
  border: 1px solid var(--secondary-gold);
  background: none;
  padding: 5px 10px;
  border-radius: 3px;
  font-size: 12px;
  transition: all 0.3s ease;
}

.language-selector .btn:hover {
  background: var(--secondary-gold);
  color: white;
}

/* Hero Section */
.hero-section {
  height: 100vh;
  position: relative;
  overflow: hidden;
  margin-top: -80px;
  background: linear-gradient(135deg, var(--primary-green) 0%, #0f3a32 100%);
}

.carousel-item {
  height: 100vh;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(20, 77, 67, 0.6);
}

.carousel-caption {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}

.hero-content h1 {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  color: white;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.text-gradient {
  color: var(--secondary-gold);
}

/* Carousel Controls */
.carousel-control-prev,
.carousel-control-next {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
}

.carousel-control-prev {
  left: 30px;
}

.carousel-control-next {
  right: 30px;
}

.carousel-indicators {
  bottom: 30px;
}

.carousel-indicators [data-bs-target] {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
}

.carousel-indicators .active {
  background: white;
}

/* Section Styles */
.architecture-section,
.resort-living-section,
.lifestyle-gallery-section {
  padding: 60px 0;
  background: #f5f2ed;
}

.section-header {
  margin-bottom: 30px;
}

.section-subtitle {
  color: var(--secondary-gold);
  font-size: 11px;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
  margin-bottom: 8px;
}

.section-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-green);
  line-height: 1.2;
  text-transform: uppercase;
  margin-bottom: 20px;
}

/* Architecture Section */
.architecture-section {
  background: #fff;
  padding: 80px 0;
}

.architecture-content p,
.resort-content p {
  font-size: 15px;
  line-height: 1.7;
  color: #555;
  text-align: justify;
  margin-bottom: 15px;
}

/* Resort Living Section */
.resort-living-section {
  background: #f5f5f0;
  padding: 80px 0;
}

/* Lifestyle Gallery Section */
.lifestyle-gallery-section {
  background: #fff;
  padding: 80px 0;
}

/* Service Management Section */
.service-management-section {
  background: #f8f9fa;
  padding: 80px 0;
}

.service-content {
  padding: 20px 0;
}

.service-content h2 {
  color: var(--primary-green);
  margin-bottom: 30px;
}

.service-content p {
  font-size: 16px;
  line-height: 1.8;
  color: #666;
  text-align: justify;
  margin-bottom: 20px;
}

/* Location Section */
.location-section {
  background: #fff;
  padding: 80px 0;
}

.location-item {
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.location-item:hover {
  background: var(--accent-light);
  transform: translateY(-5px);
}

.location-item i {
  color: var(--primary-green);
}

/* Developer Section */
.developer-section {
  background: #f8f9fa;
  padding: 80px 0;
}

.developer-stats .stat-item {
  background: #fff;
  transition: all 0.3s ease;
}

.developer-stats .stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Partners Section */
.partners-section {
  background: #fff;
  padding: 60px 0;
}

.partner-logo {
  transition: all 0.3s ease;
}

.partner-logo:hover {
  transform: scale(1.05);
}

.partner-logo img {
  max-height: 60px;
  width: auto;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.partner-logo:hover img {
  filter: grayscale(0%);
}

/* Two Column Layout */
.two-column-layout {
  display: flex;
  align-items: center;
  gap: 40px;
}

.column-image {
  flex: 0 0 40%;
}

.column-content {
  flex: 1;
}

.column-image img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

/* Gallery */
.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  margin-bottom: 30px;
}

.gallery-img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-img {
  transform: scale(1.05);
}

.gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 30px 20px 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-overlay h5 {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

/* Quality Service Section */
.quality-service-section {
  padding: 80px 0;
  background: #fff;
}

.service-content p {
  font-size: 16px;
  line-height: 1.8;
  color: #666;
  text-align: justify;
  margin-bottom: 20px;
}

/* Location Section */
.location-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.location-content p {
  font-size: 16px;
  line-height: 1.8;
  color: #666;
  text-align: justify;
  margin-bottom: 20px;
}

/* Map Section */
.map-section {
  margin-top: 50px;
}

.map-tabs {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.map-tab {
  padding: 12px 30px;
  background: transparent;
  border: 2px solid #d4af37;
  color: #d4af37;
  font-weight: 600;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.map-tab.active,
.map-tab:hover {
  background: #d4af37;
  color: white;
}

.map-container {
  height: 400px;
  background: #f8f9fa;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ddd;
}

.map-placeholder {
  text-align: center;
  color: #999;
}

.map-placeholder i {
  font-size: 4rem;
  margin-bottom: 1rem;
}

/* Developer Section */
.developer-section {
  padding: 80px 0;
  background: #fff;
}

.developer-content p {
  font-size: 16px;
  line-height: 1.8;
  color: #666;
  text-align: justify;
  margin-bottom: 20px;
}

/* Partners Section */
.partners-section {
  padding: 50px 0;
  background: #f8f9fa;
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 30px;
  align-items: center;
}

.partner-logo img {
  max-width: 100%;
  height: auto;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.partner-logo:hover img {
  filter: grayscale(0%);
}

/* Contact Form Section */
.contact-section {
  padding: 80px 0;
  background: #fff;
}

.contact-form {
  background: #f8f9fa;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.form-control {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 16px;
  margin-bottom: 20px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  border-color: #d4af37;
  box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
  outline: none;
}

.btn-primary {
  background: #d4af37;
  border: none;
  padding: 12px 40px;
  font-weight: 600;
  border-radius: 25px;
  transition: all 0.3s ease;
  color: white;
}

.btn-primary:hover {
  background: #b8941f;
  transform: translateY(-2px);
}

/* Footer */
.footer-main {
  background: var(--primary-green);
  color: white;
  padding: 60px 0 20px;
}

.footer-logo {
  max-height: 50px;
  width: auto;
}

.footer-title {
  color: var(--secondary-gold);
  margin-bottom: 20px;
  font-size: 18px;
}

.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-link {
  color: #ccc;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: var(--secondary-gold);
}

.social-links {
  margin-top: 20px;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: var(--secondary-gold);
  color: white;
  transform: translateY(-2px);
}

.contact-info {
  color: #ccc;
}

.contact-item {
  margin-bottom: 15px;
}

.contact-item i {
  color: var(--secondary-gold);
  width: 20px;
}

.copyright {
  color: #999;
  font-size: 14px;
}

.copyright a {
  color: var(--secondary-gold);
  text-decoration: none;
}

.copyright a:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding-top: 70px;
  }

  .hero-section {
    margin-top: -70px;
    height: 50vh;
  }

  .carousel-item {
    height: 50vh;
  }

  .hero-content h1 {
    font-size: 1.8rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .carousel-control-prev,
  .carousel-control-next {
    width: 40px;
    height: 40px;
  }

  .carousel-control-prev {
    left: 15px;
  }

  .carousel-control-next {
    right: 15px;
  }

  .gallery-img {
    height: 250px;
  }

  .navbar-nav {
    text-align: center;
  }

  .map-tabs {
    flex-direction: column;
    align-items: center;
  }

  .partners-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact-form {
    padding: 30px 20px;
  }

  /* Two Column Layout - Mobile */
  .two-column-layout {
    flex-direction: column;
    gap: 20px;
  }

  .column-image {
    flex: none;
  }

  .column-content {
    flex: none;
  }
}

/* ===== HIỆU ỨNG THEO TÀI LIỆU THIẾT KẾ ===== */

/* Animation Classes */
.fade-in {
  opacity: 1 !important;
  transform: translateY(0) !important;
  transition: all 0.6s ease;
}

.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Button Hover Effects */
.btn-primary,
.btn-secondary,
.cta-button {
  transition: all 0.3s ease;
  transform: scale(1);
}

.btn-primary:hover,
.btn-secondary:hover,
.cta-button:hover {
  transform: scale(1.05);
  background-color: var(--secondary-gold) !important;
  border-color: var(--secondary-gold) !important;
}

/* Gallery Hover Effects */
.gallery-item {
  overflow: hidden;
  border-radius: 10px;
}

.gallery-item img {
  transition: transform 0.3s ease;
  transform: scale(1);
}

.gallery-item:hover img {
  transform: scale(1.1);
}

.gallery-overlay {
  transition: opacity 0.3s ease;
  opacity: 0.8;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

/* Bootstrap Tab Fade */
.tab-content .tab-pane {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tab-content .tab-pane.active {
  opacity: 1;
}

/* Tooltip Hover Effects */
[data-bs-toggle="tooltip"],
.map-area {
  transition: opacity 0.3s ease;
}

[data-bs-toggle="tooltip"]:hover,
.map-area:hover {
  opacity: 0.8;
}
