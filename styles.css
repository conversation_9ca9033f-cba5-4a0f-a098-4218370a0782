/* ===== LUMIÈRE BOULEVARD - EXACT CLONE STYLES ===== */

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background: #f5f5f0;
  margin: 0;
  padding-top: 80px;
}

/* Skip Link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #d4af37;
  color: white;
  padding: 8px;
  text-decoration: none;
  z-index: 9999;
}

.skip-link:focus {
  top: 6px;
}

/* Header */
.header-main {
  background: #1a4a3a;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.navbar {
  padding: 15px 0;
}

.navbar-brand img {
  height: 40px;
}

.navbar-nav .nav-link {
  color: white !important;
  font-weight: 500;
  padding: 8px 15px !important;
  text-decoration: none;
  font-size: 14px;
  text-transform: uppercase;
}

.navbar-nav .nav-link:hover {
  color: #d4af37 !important;
}

.dropdown-menu {
  border: none;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  border-radius: 0;
  background: #1a4a3a;
}

.dropdown-item {
  color: white;
  font-size: 14px;
}

.dropdown-item:hover {
  background: #d4af37;
  color: white;
}

.language-selector .btn {
  color: #d4af37 !important;
  font-weight: 600;
  text-decoration: none;
  border: 1px solid #d4af37;
  background: none;
  padding: 5px 10px;
  border-radius: 3px;
  font-size: 12px;
}

/* Hero Section */
.hero-section {
  height: 60vh;
  position: relative;
  overflow: hidden;
  margin-top: -80px;
  background: linear-gradient(135deg, #2d5a4a 0%, #1a4a3a 100%);
}

.carousel-item {
  height: 60vh;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(26, 74, 58, 0.7);
}

.carousel-caption {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}

.hero-content h1 {
  font-size: 2.5rem;
  font-weight: bold;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  color: white;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.text-gradient {
  color: #d4af37;
}

/* Carousel Controls */
.carousel-control-prev,
.carousel-control-next {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
}

.carousel-control-prev {
  left: 30px;
}

.carousel-control-next {
  right: 30px;
}

.carousel-indicators {
  bottom: 30px;
}

.carousel-indicators [data-bs-target] {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
}

.carousel-indicators .active {
  background: white;
}

/* Section Styles */
.architecture-section,
.resort-living-section,
.lifestyle-gallery-section {
  padding: 60px 0;
  background: #f5f5f0;
}

.section-header {
  margin-bottom: 40px;
}

.section-subtitle {
  color: #d4af37;
  font-size: 12px;
  font-weight: bold;
  letter-spacing: 2px;
  text-transform: uppercase;
  margin-bottom: 10px;
}

.section-title {
  font-size: 2rem;
  font-weight: bold;
  color: #1a4a3a;
  line-height: 1.3;
  text-transform: uppercase;
}

/* Architecture Section */
.architecture-content p,
.resort-content p {
  font-size: 15px;
  line-height: 1.7;
  color: #555;
  text-align: justify;
  margin-bottom: 15px;
}

/* Resort Living Section */
.resort-living-section {
  background: #f5f5f0;
}

/* Two Column Layout */
.two-column-layout {
  display: flex;
  align-items: center;
  gap: 40px;
}

.column-image {
  flex: 0 0 40%;
}

.column-content {
  flex: 1;
}

.column-image img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

/* Gallery */
.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  margin-bottom: 30px;
}

.gallery-img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-img {
  transform: scale(1.05);
}

.gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 30px 20px 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-overlay h5 {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

/* Quality Service Section */
.quality-service-section {
  padding: 80px 0;
  background: #fff;
}

.service-content p {
  font-size: 16px;
  line-height: 1.8;
  color: #666;
  text-align: justify;
  margin-bottom: 20px;
}

/* Location Section */
.location-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.location-content p {
  font-size: 16px;
  line-height: 1.8;
  color: #666;
  text-align: justify;
  margin-bottom: 20px;
}

/* Map Section */
.map-section {
  margin-top: 50px;
}

.map-tabs {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.map-tab {
  padding: 12px 30px;
  background: transparent;
  border: 2px solid #d4af37;
  color: #d4af37;
  font-weight: 600;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.map-tab.active,
.map-tab:hover {
  background: #d4af37;
  color: white;
}

.map-container {
  height: 400px;
  background: #f8f9fa;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ddd;
}

.map-placeholder {
  text-align: center;
  color: #999;
}

.map-placeholder i {
  font-size: 4rem;
  margin-bottom: 1rem;
}

/* Developer Section */
.developer-section {
  padding: 80px 0;
  background: #fff;
}

.developer-content p {
  font-size: 16px;
  line-height: 1.8;
  color: #666;
  text-align: justify;
  margin-bottom: 20px;
}

/* Partners Section */
.partners-section {
  padding: 50px 0;
  background: #f8f9fa;
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 30px;
  align-items: center;
}

.partner-logo img {
  max-width: 100%;
  height: auto;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.partner-logo:hover img {
  filter: grayscale(0%);
}

/* Contact Form Section */
.contact-section {
  padding: 80px 0;
  background: #fff;
}

.contact-form {
  background: #f8f9fa;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.form-control {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 16px;
  margin-bottom: 20px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  border-color: #d4af37;
  box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
  outline: none;
}

.btn-primary {
  background: #d4af37;
  border: none;
  padding: 12px 40px;
  font-weight: 600;
  border-radius: 25px;
  transition: all 0.3s ease;
  color: white;
}

.btn-primary:hover {
  background: #b8941f;
  transform: translateY(-2px);
}

/* Footer */
.footer {
  background: #1a1a1a;
  color: white;
  padding: 60px 0 20px;
}

.footer h5 {
  color: #d4af37;
  margin-bottom: 20px;
  font-size: 18px;
}

.footer p,
.footer a {
  color: #ccc;
  text-decoration: none;
  line-height: 1.8;
}

.footer a:hover {
  color: #d4af37;
}

.footer-bottom {
  border-top: 1px solid #444;
  margin-top: 40px;
  padding-top: 20px;
  text-align: center;
  color: #999;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding-top: 70px;
  }

  .hero-section {
    margin-top: -70px;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .carousel-control-prev,
  .carousel-control-next {
    width: 40px;
    height: 40px;
  }

  .carousel-control-prev {
    left: 15px;
  }

  .carousel-control-next {
    right: 15px;
  }

  .gallery-img {
    height: 250px;
  }

  .navbar-nav {
    text-align: center;
  }

  .map-tabs {
    flex-direction: column;
    align-items: center;
  }

  .partners-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact-form {
    padding: 30px 20px;
  }
}
